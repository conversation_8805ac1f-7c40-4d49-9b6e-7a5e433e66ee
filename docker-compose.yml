version: '3.8'

services:
  vdb-server:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      # 如果需要持久化日志，可以挂载日志目录
      - ./logs:/app/logs
    networks:
      - vdb-network

networks:
  vdb-network:
    driver: bridge
