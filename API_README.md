# VDB Server Flask API

这是一个基于 Flask 的 HTTP API 服务，提供了对 `single_sync` 和 `single_delete` 方法的 HTTP 接口封装。

## 功能说明

- **同步接口**: 将 YApi 接口信息同步到向量数据库
- **删除接口**: 从向量数据库中删除指定接口信息
- **健康检查**: 检查服务运行状态

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务

```bash
python flask_app.py
```

服务将在 `http://localhost:5000` 启动。

## API 接口

### 1. 同步接口

**接口地址**: `POST /api/sync/interface`

**请求参数**:
```json
{
    "interface_id": 1103
}
```

**成功响应**:
```json
{
    "success": true,
    "message": "接口同步成功（新增）",
    "interface_id": 1103,
    "operation": "insert"
}
```

**失败响应**:
```json
{
    "success": false,
    "message": "接口不存在或获取失败",
    "interface_id": 1103
}
```

**curl 示例**:
```bash
curl -X POST http://localhost:5000/api/sync/interface \
  -H "Content-Type: application/json" \
  -d '{"interface_id": 1103}'
```

### 2. 删除接口

**接口地址**: `DELETE /api/delete/interface`

**请求参数**:
```json
{
    "interface_id": 1103
}
```

**成功响应**:
```json
{
    "success": true,
    "message": "接口删除成功",
    "interface_id": 1103
}
```

**失败响应**:
```json
{
    "success": false,
    "message": "接口删除失败，状态码: 404",
    "interface_id": 1103
}
```

**curl 示例**:
```bash
curl -X DELETE http://localhost:5000/api/delete/interface \
  -H "Content-Type: application/json" \
  -d '{"interface_id": 1103}'
```

### 3. 健康检查

**接口地址**: `GET /api/health`

**响应**:
```json
{
    "status": "healthy",
    "message": "VDB Server API is running",
    "timestamp": **********
}
```

**curl 示例**:
```bash
curl http://localhost:5000/api/health
```

## 错误处理

API 使用标准的 HTTP 状态码：

- `200`: 成功
- `400`: 请求参数错误
- `404`: 接口不存在
- `405`: 请求方法不被允许
- `500`: 服务器内部错误

所有错误响应都包含以下格式：
```json
{
    "success": false,
    "message": "错误描述信息"
}
```

## 配置说明

在 `flask_app.py` 中可以修改以下配置：

- **YApi 配置**: `yapi_base_url`, `yapi_token`, `yapi_catid`
- **向量数据库配置**: `vdb_base_url`, `vdb_headers`, `vdb_class_name`
- **嵌入模型配置**: `embed_url`, `embed_headers`
- **Flask 服务配置**: 在 `app.run()` 中修改 `host` 和 `port`

## 注意事项

1. 确保 YApi 服务、向量数据库服务和嵌入模型服务都可以正常访问
2. 检查配置中的 URL、Token 等信息是否正确
3. 建议在生产环境中使用 WSGI 服务器（如 Gunicorn）而不是 Flask 内置的开发服务器
4. 可以根据需要添加认证、限流等安全措施

## 生产部署建议

使用 Gunicorn 部署：

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 flask_app:app
```

或者使用 Docker：

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY flask_app.py .
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "flask_app:app"]
```
