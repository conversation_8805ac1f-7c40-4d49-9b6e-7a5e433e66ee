"""API接口测试"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch

from src.main import create_app


@pytest.fixture
def client():
    """测试客户端"""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def mock_kb_service():
    """模拟知识库服务"""
    service = AsyncMock()
    service.get_statistics.return_value = {
        "total_interfaces": 100,
        "total_projects": 5,
        "projects": []
    }
    service.search_interfaces.return_value = [
        {
            "id": "interface_1",
            "interface_id": 1,
            "title": "用户登录",
            "description": "用户登录接口",
            "similarity": 0.95
        }
    ]
    return service


def test_health_check(client):
    """测试健康检查"""
    response = client.get("/api/v1/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "VDB-Server"


def test_root_endpoint(client):
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "service" in data
    assert "version" in data
    assert "status" in data


@patch('src.api.routes.kb_service')
def test_get_statistics(mock_service, client, mock_kb_service):
    """测试获取统计信息"""
    mock_service = mock_kb_service
    
    response = client.get("/api/v1/stats")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "data" in data


@patch('src.api.routes.kb_service')
def test_search_interfaces_post(mock_service, client, mock_kb_service):
    """测试POST搜索接口"""
    mock_service = mock_kb_service
    
    search_data = {
        "query": "用户登录",
        "top_k": 10
    }
    
    response = client.post("/api/v1/search", json=search_data)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "results" in data["data"]


@patch('src.api.routes.kb_service')
def test_search_interfaces_get(mock_service, client, mock_kb_service):
    """测试GET搜索接口"""
    mock_service = mock_kb_service
    
    response = client.get("/api/v1/search?q=用户登录&limit=10")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "results" in data["data"]


@patch('src.api.routes.kb_service')
def test_sync_project(mock_service, client, mock_kb_service):
    """测试同步项目"""
    mock_service = mock_kb_service
    
    sync_data = {"project_id": 123}
    
    response = client.post("/api/v1/sync/project", json=sync_data)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True


@patch('src.api.routes.kb_service')
def test_sync_interface(mock_service, client, mock_kb_service):
    """测试同步接口"""
    mock_service = mock_kb_service
    mock_service.sync_interface.return_value = {
        "interface_id": 1,
        "success": True,
        "error": None
    }
    
    sync_data = {"interface_id": 1}
    
    response = client.post("/api/v1/sync/interface", json=sync_data)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True


@patch('src.api.routes.kb_service')
def test_update_interface(mock_service, client, mock_kb_service):
    """测试更新接口"""
    mock_service = mock_kb_service
    mock_service.update_interface.return_value = {
        "interface_id": 1,
        "success": True,
        "error": None
    }
    
    response = client.put("/api/v1/interface/1")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True


@patch('src.api.routes.kb_service')
def test_delete_interface(mock_service, client, mock_kb_service):
    """测试删除接口"""
    mock_service = mock_kb_service
    mock_service.delete_interface.return_value = {
        "interface_id": 1,
        "success": True,
        "error": None
    }
    
    response = client.delete("/api/v1/interface/1")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True


@patch('src.api.routes.kb_service')
def test_yapi_webhook(mock_service, client, mock_kb_service):
    """测试YAPI Webhook"""
    mock_service = mock_kb_service
    
    webhook_data = {
        "action": "add",
        "interface_id": 1,
        "project_id": 123
    }
    
    response = client.post("/api/v1/webhook/yapi", json=webhook_data)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True


def test_search_missing_query(client):
    """测试搜索缺少查询参数"""
    response = client.get("/api/v1/search")
    assert response.status_code == 422  # Validation error


def test_webhook_missing_interface_id(client):
    """测试Webhook缺少interface_id"""
    webhook_data = {
        "action": "add",
        "project_id": 123
    }
    
    with patch('src.api.routes.kb_service'):
        response = client.post("/api/v1/webhook/yapi", json=webhook_data)
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is False
        assert "缺少interface_id参数" in data["message"]
