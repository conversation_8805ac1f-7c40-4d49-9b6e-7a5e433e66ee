# 使用稳定版基础镜像
FROM python:3.9-bookworm

WORKDIR /app

# 禁用 Python 缓存
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# 使用阿里云镜像源（完整替换 sources.list）
RUN echo 'deb https://mirrors.aliyun.com/debian/ bookworm main non-free non-free-firmware\n' \
        'deb https://mirrors.aliyun.com/debian/ bookworm-updates main non-free non-free-firmware\n' \
        'deb https://mirrors.aliyun.com/debian-security/ bookworm-security main non-free non-free-firmware' > /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y gcc curl && \
    rm -rf /var/lib/apt/lists/*

# 安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制代码
COPY flask_app.py .
COPY start_server.py .

# 创建非 root 用户
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/api/health || exit 1

CMD ["python", "start_server.py", "--production", "--host", "0.0.0.0", "--port", "5000"]