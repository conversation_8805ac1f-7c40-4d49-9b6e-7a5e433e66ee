"""YAPI客户端服务"""
import httpx
from typing import List, Optional, Dict, Any
from loguru import logger

from ..models.api_interface import APIInterface, APIInterfaceList


class YAPIClient:
    """YAPI客户端"""
    
    def __init__(self, base_url: str, token: Optional[str] = None):
        """
        初始化YAPI客户端
        
        Args:
            base_url: YAPI服务器地址
            token: 访问令牌
        """
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.client = httpx.AsyncClient(timeout=30.0)
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
        
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()
        
    def _build_url(self, endpoint: str) -> str:
        """构建完整URL"""
        return f"{self.base_url}{endpoint}"
        
    def _build_params(self, **kwargs) -> Dict[str, Any]:
        """构建请求参数"""
        params = {}
        if self.token:
            params['token'] = self.token
        params.update(kwargs)
        return params
        
    async def get_project_interfaces(
        self, 
        project_id: int, 
        page: int = 1, 
        limit: int = 100
    ) -> List[APIInterface]:
        """
        获取项目的接口列表
        
        Args:
            project_id: 项目ID
            page: 页码
            limit: 每页数量
            
        Returns:
            接口列表
        """
        try:
            url = self._build_url("/api/interface/list")
            params = self._build_params(
                project_id=project_id,
                page=page,
                limit=limit
            )
            
            logger.info(f"获取项目 {project_id} 的接口列表, 页码: {page}, 限制: {limit}")
            
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            interface_list = APIInterfaceList(**data)
            
            if interface_list.errcode != 0:
                logger.error(f"YAPI返回错误: {interface_list.errmsg}")
                return []
                
            interfaces = interface_list.get_interfaces()
            logger.info(f"成功获取 {len(interfaces)} 个接口")
            
            return interfaces
            
        except Exception as e:
            logger.error(f"获取接口列表失败: {e}")
            return []
            
    async def get_interface_detail(self, interface_id: int) -> Optional[APIInterface]:
        """
        获取接口详情
        
        Args:
            interface_id: 接口ID
            
        Returns:
            接口详情
        """
        try:
            url = self._build_url("/api/interface/get")
            params = self._build_params(id=interface_id)
            
            logger.info(f"获取接口 {interface_id} 的详情")
            
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('errcode') != 0:
                logger.error(f"YAPI返回错误: {data.get('errmsg')}")
                return None
                
            interface_data = data.get('data')
            if not interface_data:
                logger.warning(f"接口 {interface_id} 无数据")
                return None
                
            interface = APIInterface(**interface_data)
            logger.info(f"成功获取接口详情: {interface.title}")
            
            return interface
            
        except Exception as e:
            logger.error(f"获取接口详情失败: {e}")
            return None
            
    async def get_all_project_interfaces(self, project_id: int) -> List[APIInterface]:
        """
        获取项目的所有接口（分页获取）
        
        Args:
            project_id: 项目ID
            
        Returns:
            所有接口列表
        """
        all_interfaces = []
        page = 1
        limit = 100
        
        while True:
            interfaces = await self.get_project_interfaces(
                project_id=project_id,
                page=page,
                limit=limit
            )
            
            if not interfaces:
                break
                
            all_interfaces.extend(interfaces)
            
            # 如果返回的接口数量小于限制，说明已经是最后一页
            if len(interfaces) < limit:
                break
                
            page += 1
            
        logger.info(f"项目 {project_id} 总共获取到 {len(all_interfaces)} 个接口")
        return all_interfaces
        
    async def get_projects(self) -> List[Dict[str, Any]]:
        """
        获取项目列表
        
        Returns:
            项目列表
        """
        try:
            url = self._build_url("/api/project/list")
            params = self._build_params()
            
            logger.info("获取项目列表")
            
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('errcode') != 0:
                logger.error(f"YAPI返回错误: {data.get('errmsg')}")
                return []
                
            projects = data.get('data', [])
            logger.info(f"成功获取 {len(projects)} 个项目")
            
            return projects
            
        except Exception as e:
            logger.error(f"获取项目列表失败: {e}")
            return []
