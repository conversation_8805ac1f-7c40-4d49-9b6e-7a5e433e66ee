# VDB Server 环境变量配置示例
# 复制此文件为 .env 并修改相应的值

# YApi 配置
YAPI_BASE_URL=http://************:3000/api/interface/
YAPI_TOKEN=e84e14ddd978c534dd2aa872b4e5ae79e989dcea0d24b47b6da1686b96b81d84
YAPI_CATID=303

# 向量数据库配置
VDB_BASE_URL=http://************:8009/v1/
VDB_AUTHORIZATION=Bearer QTsBTQWbr6iESeOoVKgiejFDhQogiSON
VDB_CLASS_NAME=Crm_Ai_Interface

# 嵌入模型配置
EMBED_URL=https://ai.secsign.online:3003/v1/embeddings
EMBED_AUTHORIZATION=Bearer sk-hBB0Icl2AlA58nOuHydvHRX33Cagdl3etG04k0AI4F4fzDvG

# Flask 应用配置
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=false

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=vdb_server.log

# HTTP 请求配置
REQUEST_TIMEOUT=30
REQUEST_RETRIES=3
