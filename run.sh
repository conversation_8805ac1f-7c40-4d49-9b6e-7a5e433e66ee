#!/bin/bash

# VDB Server 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 Python 环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_message $RED "错误: 未找到 Python3"
        exit 1
    fi
    
    python_version=$(python3 --version 2>&1 | awk '{print $2}')
    print_message $GREEN "Python 版本: $python_version"
}

# 检查依赖
check_dependencies() {
    print_message $BLUE "检查依赖..."
    
    if [ ! -f "requirements.txt" ]; then
        print_message $RED "错误: 未找到 requirements.txt"
        exit 1
    fi
    
    # 检查是否需要安装依赖
    if ! python3 -c "import flask, httpx, requests" &> /dev/null; then
        print_message $YELLOW "安装依赖..."
        pip3 install -r requirements.txt
    else
        print_message $GREEN "依赖已满足"
    fi
}

# 检查配置
check_config() {
    print_message $BLUE "验证配置..."
    
    if python3 -c "from config import config; exit(0 if config.validate_config() else 1)"; then
        print_message $GREEN "配置验证通过"
    else
        print_message $RED "配置验证失败，请检查配置文件"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "VDB Server 启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  cli                 启动交互式命令行界面 (默认)"
    echo "  api                 启动 Flask API 服务"
    echo "  api-dev             启动 Flask API 服务 (开发模式)"
    echo "  api-prod            启动 Flask API 服务 (生产模式)"
    echo "  test                运行 API 测试"
    echo "  example             运行使用示例"
    echo "  config              显示配置信息"
    echo "  help                显示此帮助信息"
    echo ""
}

# 主函数
main() {
    local mode=${1:-cli}
    
    print_message $BLUE "=== VDB Server 启动脚本 ==="
    
    case $mode in
        "cli")
            check_python
            check_dependencies
            check_config
            print_message $GREEN "启动交互式命令行界面..."
            python3 app.py
            ;;
        "api")
            check_python
            check_dependencies
            check_config
            print_message $GREEN "启动 Flask API 服务..."
            python3 flask_app.py
            ;;
        "api-dev")
            check_python
            check_dependencies
            check_config
            print_message $GREEN "启动 Flask API 服务 (开发模式)..."
            python3 start_server.py --debug
            ;;
        "api-prod")
            check_python
            check_dependencies
            check_config
            print_message $GREEN "启动 Flask API 服务 (生产模式)..."
            python3 start_server.py --production --workers 4
            ;;
        "test")
            check_python
            check_dependencies
            print_message $GREEN "运行 API 测试..."
            python3 test_api.py
            ;;
        "example")
            check_python
            check_dependencies
            print_message $GREEN "运行使用示例..."
            python3 example_usage.py
            ;;
        "config")
            check_python
            print_message $GREEN "显示配置信息..."
            python3 config.py
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_message $RED "未知选项: $mode"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
