"""知识库构建服务"""
import asyncio
from typing import List, Dict, Any, Optional
from loguru import logger

from ..models.api_interface import APIInterface
from .yapi_client import YAPIClient
from .embedding_service import EmbeddingService
from ..database.vector_db import VectorDatabase


class KnowledgeBaseService:
    """知识库构建服务"""
    
    def __init__(
        self,
        yapi_base_url: str,
        yapi_token: Optional[str] = None,
        embedding_model: str = "all-MiniLM-L6-v2",
        vector_db_path: str = "./data/chroma_db"
    ):
        """
        初始化知识库服务
        
        Args:
            yapi_base_url: YAPI服务器地址
            yapi_token: YAPI访问令牌
            embedding_model: 嵌入模型名称
            vector_db_path: 向量数据库路径
        """
        self.yapi_client = YAPIClient(yapi_base_url, yapi_token)
        self.embedding_service = EmbeddingService(embedding_model)
        self.vector_db = VectorDatabase(vector_db_path)
        
    async def initialize(self):
        """初始化服务"""
        try:
            await self.vector_db.initialize()
            logger.info("知识库服务初始化完成")
        except Exception as e:
            logger.error(f"知识库服务初始化失败: {e}")
            raise
            
    async def close(self):
        """关闭服务"""
        try:
            await self.yapi_client.close()
            await self.vector_db.close()
            logger.info("知识库服务已关闭")
        except Exception as e:
            logger.error(f"关闭知识库服务失败: {e}")
            
    async def sync_project_interfaces(self, project_id: int) -> Dict[str, Any]:
        """
        同步项目接口到知识库
        
        Args:
            project_id: 项目ID
            
        Returns:
            同步结果统计
        """
        result = {
            "project_id": project_id,
            "total_interfaces": 0,
            "processed_interfaces": 0,
            "failed_interfaces": 0,
            "errors": []
        }
        
        try:
            logger.info(f"开始同步项目 {project_id} 的接口")
            
            # 1. 从YAPI获取接口列表
            interfaces = await self.yapi_client.get_all_project_interfaces(project_id)
            result["total_interfaces"] = len(interfaces)
            
            if not interfaces:
                logger.warning(f"项目 {project_id} 没有找到接口")
                return result
                
            logger.info(f"获取到 {len(interfaces)} 个接口，开始处理")
            
            # 2. 批量向量化接口
            try:
                vectors = await self.embedding_service.encode_interfaces(interfaces)
                logger.info(f"接口向量化完成，共 {len(vectors)} 个向量")
            except Exception as e:
                error_msg = f"接口向量化失败: {e}"
                logger.error(error_msg)
                result["errors"].append(error_msg)
                return result
                
            # 3. 批量保存到向量数据库
            try:
                processed_count = await self.vector_db.add_interfaces_batch(
                    interfaces, vectors
                )
                result["processed_interfaces"] = processed_count
                result["failed_interfaces"] = len(interfaces) - processed_count
                
                logger.info(f"接口同步完成，成功: {processed_count}, 失败: {result['failed_interfaces']}")
                
            except Exception as e:
                error_msg = f"保存到向量数据库失败: {e}"
                logger.error(error_msg)
                result["errors"].append(error_msg)
                
        except Exception as e:
            error_msg = f"同步项目接口失败: {e}"
            logger.error(error_msg)
            result["errors"].append(error_msg)
            
        return result
        
    async def sync_interface(self, interface_id: int) -> Dict[str, Any]:
        """
        同步单个接口到知识库
        
        Args:
            interface_id: 接口ID
            
        Returns:
            同步结果
        """
        result = {
            "interface_id": interface_id,
            "success": False,
            "error": None
        }
        
        try:
            logger.info(f"开始同步接口 {interface_id}")
            
            # 1. 获取接口详情
            interface = await self.yapi_client.get_interface_detail(interface_id)
            if not interface:
                result["error"] = "接口不存在或获取失败"
                return result
                
            # 2. 向量化接口
            vector = await self.embedding_service.encode_interface(interface)
            
            # 3. 保存到向量数据库
            success = await self.vector_db.add_interface(interface, vector)
            
            result["success"] = success
            if success:
                logger.info(f"接口 {interface_id} 同步成功")
            else:
                result["error"] = "保存到向量数据库失败"
                
        except Exception as e:
            error_msg = f"同步接口失败: {e}"
            logger.error(error_msg)
            result["error"] = error_msg
            
        return result
        
    async def update_interface(self, interface_id: int) -> Dict[str, Any]:
        """
        更新接口信息
        
        Args:
            interface_id: 接口ID
            
        Returns:
            更新结果
        """
        result = {
            "interface_id": interface_id,
            "success": False,
            "error": None
        }
        
        try:
            logger.info(f"开始更新接口 {interface_id}")
            
            # 1. 获取最新接口详情
            interface = await self.yapi_client.get_interface_detail(interface_id)
            if not interface:
                result["error"] = "接口不存在或获取失败"
                return result
                
            # 2. 重新向量化接口
            vector = await self.embedding_service.encode_interface(interface)
            
            # 3. 更新向量数据库
            success = await self.vector_db.update_interface(interface, vector)
            
            result["success"] = success
            if success:
                logger.info(f"接口 {interface_id} 更新成功")
            else:
                result["error"] = "更新向量数据库失败"
                
        except Exception as e:
            error_msg = f"更新接口失败: {e}"
            logger.error(error_msg)
            result["error"] = error_msg
            
        return result
        
    async def delete_interface(self, interface_id: int) -> Dict[str, Any]:
        """
        删除接口
        
        Args:
            interface_id: 接口ID
            
        Returns:
            删除结果
        """
        result = {
            "interface_id": interface_id,
            "success": False,
            "error": None
        }
        
        try:
            logger.info(f"开始删除接口 {interface_id}")
            
            success = await self.vector_db.delete_interface(interface_id)
            
            result["success"] = success
            if success:
                logger.info(f"接口 {interface_id} 删除成功")
            else:
                result["error"] = "从向量数据库删除失败"
                
        except Exception as e:
            error_msg = f"删除接口失败: {e}"
            logger.error(error_msg)
            result["error"] = error_msg
            
        return result
        
    async def search_interfaces(
        self, 
        query: str, 
        top_k: int = 10,
        project_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        搜索相似接口
        
        Args:
            query: 搜索查询
            top_k: 返回数量
            project_id: 项目ID过滤
            
        Returns:
            相似接口列表
        """
        try:
            logger.info(f"搜索接口: {query}")
            
            # 1. 将查询向量化
            query_vector = await self.embedding_service.encode_text(query)
            
            # 2. 构建过滤条件
            where_condition = None
            if project_id:
                where_condition = {"project_id": project_id}
                
            # 3. 搜索相似接口
            similar_interfaces = await self.vector_db.search_similar(
                query_vector=query_vector,
                top_k=top_k,
                where=where_condition
            )
            
            logger.info(f"搜索完成，找到 {len(similar_interfaces)} 个相似接口")
            return similar_interfaces
            
        except Exception as e:
            logger.error(f"搜索接口失败: {e}")
            return []
            
    async def get_statistics(self) -> Dict[str, Any]:
        """
        获取知识库统计信息
        
        Returns:
            统计信息
        """
        try:
            # 获取接口总数
            total_interfaces = await self.vector_db.get_interface_count()
            
            # 获取项目列表
            projects = await self.yapi_client.get_projects()
            
            stats = {
                "total_interfaces": total_interfaces,
                "total_projects": len(projects),
                "projects": projects
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                "total_interfaces": 0,
                "total_projects": 0,
                "projects": [],
                "error": str(e)
            }
            
    async def rebuild_knowledge_base(self) -> Dict[str, Any]:
        """
        重建知识库
        
        Returns:
            重建结果
        """
        result = {
            "success": False,
            "total_projects": 0,
            "processed_projects": 0,
            "total_interfaces": 0,
            "processed_interfaces": 0,
            "errors": []
        }
        
        try:
            logger.info("开始重建知识库")
            
            # 1. 清空现有数据
            await self.vector_db.clear_all()
            logger.info("已清空现有知识库数据")
            
            # 2. 获取所有项目
            projects = await self.yapi_client.get_projects()
            result["total_projects"] = len(projects)
            
            if not projects:
                logger.warning("没有找到项目")
                return result
                
            # 3. 逐个同步项目
            for project in projects:
                project_id = project.get("_id")
                if not project_id:
                    continue
                    
                try:
                    sync_result = await self.sync_project_interfaces(project_id)
                    result["total_interfaces"] += sync_result["total_interfaces"]
                    result["processed_interfaces"] += sync_result["processed_interfaces"]
                    result["processed_projects"] += 1
                    
                    if sync_result["errors"]:
                        result["errors"].extend(sync_result["errors"])
                        
                except Exception as e:
                    error_msg = f"同步项目 {project_id} 失败: {e}"
                    logger.error(error_msg)
                    result["errors"].append(error_msg)
                    
            result["success"] = True
            logger.info(f"知识库重建完成，处理项目: {result['processed_projects']}/{result['total_projects']}, 处理接口: {result['processed_interfaces']}/{result['total_interfaces']}")
            
        except Exception as e:
            error_msg = f"重建知识库失败: {e}"
            logger.error(error_msg)
            result["errors"].append(error_msg)
            
        return result
