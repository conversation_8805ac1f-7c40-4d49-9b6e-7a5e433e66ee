"""向量数据库操作"""
import chromadb
from chromadb.config import Settings
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
import json
from datetime import datetime

from ..models.api_interface import APIInterface, VectorRecord


class VectorDatabase:
    """向量数据库操作类"""
    
    def __init__(self, persist_directory: str = "./data/chroma_db"):
        """
        初始化向量数据库
        
        Args:
            persist_directory: 数据持久化目录
        """
        self.persist_directory = persist_directory
        self.client = None
        self.collection = None
        self.collection_name = "api_interfaces"
        
    async def initialize(self):
        """初始化数据库连接"""
        try:
            # 创建ChromaDB客户端
            self.client = chromadb.PersistentClient(
                path=self.persist_directory,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # 获取或创建集合
            try:
                self.collection = self.client.get_collection(
                    name=self.collection_name
                )
                logger.info(f"连接到现有集合: {self.collection_name}")
            except Exception:
                self.collection = self.client.create_collection(
                    name=self.collection_name,
                    metadata={"description": "API接口向量存储"}
                )
                logger.info(f"创建新集合: {self.collection_name}")
                
            logger.info("向量数据库初始化完成")
            
        except Exception as e:
            logger.error(f"向量数据库初始化失败: {e}")
            raise
            
    async def close(self):
        """关闭数据库连接"""
        # ChromaDB客户端不需要显式关闭
        pass
        
    def _interface_to_metadata(self, interface: APIInterface) -> Dict[str, Any]:
        """将接口对象转换为元数据"""
        metadata = {
            "interface_id": interface.id,
            "title": interface.title,
            "description": interface.description or "",
            "method": interface.method,
            "path": interface.path,
            "project_id": interface.project_id,
            "category_id": interface.category_id,
            "status": interface.status,
            "username": interface.username or "",
            "add_time": interface.add_time.isoformat() if interface.add_time else "",
            "up_time": interface.up_time.isoformat() if interface.up_time else "",
        }
        
        # 添加参数信息
        if interface.req_params:
            metadata["req_params"] = json.dumps([p.dict() for p in interface.req_params])
        if interface.req_query:
            metadata["req_query"] = json.dumps([p.dict() for p in interface.req_query])
        if interface.req_headers:
            metadata["req_headers"] = json.dumps([p.dict() for p in interface.req_headers])
        if interface.req_body_form:
            metadata["req_body_form"] = json.dumps([p.dict() for p in interface.req_body_form])
            
        return metadata
        
    async def add_interface(
        self, 
        interface: APIInterface, 
        vector: List[float]
    ) -> bool:
        """
        添加接口向量
        
        Args:
            interface: 接口对象
            vector: 向量数据
            
        Returns:
            是否成功
        """
        try:
            if not self.collection:
                await self.initialize()
                
            # 准备数据
            doc_id = f"interface_{interface.id}"
            document = interface.get_full_description()
            metadata = self._interface_to_metadata(interface)
            
            # 添加到集合
            self.collection.add(
                ids=[doc_id],
                embeddings=[vector],
                documents=[document],
                metadatas=[metadata]
            )
            
            logger.info(f"成功添加接口向量: {interface.id} - {interface.title}")
            return True
            
        except Exception as e:
            logger.error(f"添加接口向量失败: {e}")
            return False
            
    async def add_interfaces_batch(
        self, 
        interfaces: List[APIInterface], 
        vectors: List[List[float]]
    ) -> int:
        """
        批量添加接口向量
        
        Args:
            interfaces: 接口列表
            vectors: 向量列表
            
        Returns:
            成功添加的数量
        """
        try:
            if not self.collection:
                await self.initialize()
                
            if len(interfaces) != len(vectors):
                raise ValueError("接口数量与向量数量不匹配")
                
            # 准备批量数据
            ids = []
            documents = []
            embeddings = []
            metadatas = []
            
            for interface, vector in zip(interfaces, vectors):
                doc_id = f"interface_{interface.id}"
                document = interface.get_full_description()
                metadata = self._interface_to_metadata(interface)
                
                ids.append(doc_id)
                documents.append(document)
                embeddings.append(vector)
                metadatas.append(metadata)
                
            # 批量添加
            self.collection.add(
                ids=ids,
                embeddings=embeddings,
                documents=documents,
                metadatas=metadatas
            )
            
            logger.info(f"批量添加接口向量完成: {len(interfaces)} 个")
            return len(interfaces)
            
        except Exception as e:
            logger.error(f"批量添加接口向量失败: {e}")
            return 0
            
    async def update_interface(
        self, 
        interface: APIInterface, 
        vector: List[float]
    ) -> bool:
        """
        更新接口向量
        
        Args:
            interface: 接口对象
            vector: 向量数据
            
        Returns:
            是否成功
        """
        try:
            if not self.collection:
                await self.initialize()
                
            # 准备数据
            doc_id = f"interface_{interface.id}"
            document = interface.get_full_description()
            metadata = self._interface_to_metadata(interface)
            
            # 更新集合
            self.collection.update(
                ids=[doc_id],
                embeddings=[vector],
                documents=[document],
                metadatas=[metadata]
            )
            
            logger.info(f"成功更新接口向量: {interface.id} - {interface.title}")
            return True
            
        except Exception as e:
            logger.error(f"更新接口向量失败: {e}")
            return False
            
    async def delete_interface(self, interface_id: int) -> bool:
        """
        删除接口向量
        
        Args:
            interface_id: 接口ID
            
        Returns:
            是否成功
        """
        try:
            if not self.collection:
                await self.initialize()
                
            doc_id = f"interface_{interface_id}"
            
            # 删除文档
            self.collection.delete(ids=[doc_id])
            
            logger.info(f"成功删除接口向量: {interface_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除接口向量失败: {e}")
            return False
            
    async def search_similar(
        self, 
        query_vector: List[float], 
        top_k: int = 10,
        where: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        搜索相似接口
        
        Args:
            query_vector: 查询向量
            top_k: 返回数量
            where: 过滤条件
            
        Returns:
            相似接口列表
        """
        try:
            if not self.collection:
                await self.initialize()
                
            # 执行相似性搜索
            results = self.collection.query(
                query_embeddings=[query_vector],
                n_results=top_k,
                where=where,
                include=["metadatas", "documents", "distances"]
            )
            
            # 格式化结果
            similar_interfaces = []
            if results["ids"] and results["ids"][0]:
                for i, doc_id in enumerate(results["ids"][0]):
                    interface_data = {
                        "id": doc_id,
                        "interface_id": results["metadatas"][0][i]["interface_id"],
                        "title": results["metadatas"][0][i]["title"],
                        "description": results["documents"][0][i],
                        "similarity": 1 - results["distances"][0][i],  # 转换为相似度
                        "metadata": results["metadatas"][0][i]
                    }
                    similar_interfaces.append(interface_data)
                    
            logger.info(f"搜索到 {len(similar_interfaces)} 个相似接口")
            return similar_interfaces
            
        except Exception as e:
            logger.error(f"搜索相似接口失败: {e}")
            return []
            
    async def get_interface_count(self) -> int:
        """
        获取接口总数
        
        Returns:
            接口数量
        """
        try:
            if not self.collection:
                await self.initialize()
                
            count = self.collection.count()
            return count
            
        except Exception as e:
            logger.error(f"获取接口数量失败: {e}")
            return 0
            
    async def clear_all(self) -> bool:
        """
        清空所有数据
        
        Returns:
            是否成功
        """
        try:
            if not self.collection:
                await self.initialize()
                
            # 删除集合
            self.client.delete_collection(name=self.collection_name)
            
            # 重新创建集合
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "API接口向量存储"}
            )
            
            logger.info("成功清空向量数据库")
            return True
            
        except Exception as e:
            logger.error(f"清空向量数据库失败: {e}")
            return False
