@echo off
REM VDB Server 启动脚本 (Windows 版本)

setlocal enabledelayedexpansion

REM 设置颜色
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 打印带颜色的消息
:print_message
echo %~2
goto :eof

REM 检查 Python 环境
:check_python
python --version >nul 2>&1
if errorlevel 1 (
    echo %RED%错误: 未找到 Python%NC%
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo %GREEN%Python 版本: !python_version!%NC%
goto :eof

REM 检查依赖
:check_dependencies
echo %BLUE%检查依赖...%NC%

if not exist "requirements.txt" (
    echo %RED%错误: 未找到 requirements.txt%NC%
    exit /b 1
)

REM 检查是否需要安装依赖
python -c "import flask, httpx, requests" >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%安装依赖...%NC%
    pip install -r requirements.txt
) else (
    echo %GREEN%依赖已满足%NC%
)
goto :eof

REM 检查配置
:check_config
echo %BLUE%验证配置...%NC%

python -c "from config import config; exit(0 if config.validate_config() else 1)" >nul 2>&1
if errorlevel 1 (
    echo %RED%配置验证失败，请检查配置文件%NC%
    exit /b 1
) else (
    echo %GREEN%配置验证通过%NC%
)
goto :eof

REM 显示帮助信息
:show_help
echo VDB Server 启动脚本
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   cli                 启动交互式命令行界面 (默认)
echo   api                 启动 Flask API 服务
echo   api-dev             启动 Flask API 服务 (开发模式)
echo   api-prod            启动 Flask API 服务 (生产模式)
echo   test                运行 API 测试
echo   example             运行使用示例
echo   config              显示配置信息
echo   help                显示此帮助信息
echo.
goto :eof

REM 主函数
:main
set "mode=%~1"
if "%mode%"=="" set "mode=cli"

echo %BLUE%=== VDB Server 启动脚本 ===%NC%

if "%mode%"=="cli" (
    call :check_python
    if errorlevel 1 exit /b 1
    call :check_dependencies
    if errorlevel 1 exit /b 1
    call :check_config
    if errorlevel 1 exit /b 1
    echo %GREEN%启动交互式命令行界面...%NC%
    python app.py
) else if "%mode%"=="api" (
    call :check_python
    if errorlevel 1 exit /b 1
    call :check_dependencies
    if errorlevel 1 exit /b 1
    call :check_config
    if errorlevel 1 exit /b 1
    echo %GREEN%启动 Flask API 服务...%NC%
    python flask_app.py
) else if "%mode%"=="api-dev" (
    call :check_python
    if errorlevel 1 exit /b 1
    call :check_dependencies
    if errorlevel 1 exit /b 1
    call :check_config
    if errorlevel 1 exit /b 1
    echo %GREEN%启动 Flask API 服务 (开发模式)...%NC%
    python start_server.py --debug
) else if "%mode%"=="api-prod" (
    call :check_python
    if errorlevel 1 exit /b 1
    call :check_dependencies
    if errorlevel 1 exit /b 1
    call :check_config
    if errorlevel 1 exit /b 1
    echo %GREEN%启动 Flask API 服务 (生产模式)...%NC%
    python start_server.py --production --workers 4
) else if "%mode%"=="test" (
    call :check_python
    if errorlevel 1 exit /b 1
    call :check_dependencies
    if errorlevel 1 exit /b 1
    echo %GREEN%运行 API 测试...%NC%
    python test_api.py
) else if "%mode%"=="example" (
    call :check_python
    if errorlevel 1 exit /b 1
    call :check_dependencies
    if errorlevel 1 exit /b 1
    echo %GREEN%运行使用示例...%NC%
    python example_usage.py
) else if "%mode%"=="config" (
    call :check_python
    if errorlevel 1 exit /b 1
    echo %GREEN%显示配置信息...%NC%
    python config.py
) else if "%mode%"=="help" (
    call :show_help
) else if "%mode%"=="-h" (
    call :show_help
) else if "%mode%"=="--help" (
    call :show_help
) else (
    echo %RED%未知选项: %mode%%NC%
    call :show_help
    exit /b 1
)

goto :eof

REM 执行主函数
call :main %*
