"""API接口数据模型"""
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime


class APIParameter(BaseModel):
    """API参数模型"""
    name: str = Field(..., description="参数名称")
    type: str = Field(..., description="参数类型")
    required: bool = Field(default=False, description="是否必需")
    description: Optional[str] = Field(None, description="参数描述")
    example: Optional[Any] = Field(None, description="参数示例")


class APIInterface(BaseModel):
    """API接口模型"""
    id: int = Field(..., description="接口ID")
    title: str = Field(..., description="接口标题")
    description: Optional[str] = Field(None, description="接口描述")
    method: str = Field(..., description="HTTP方法")
    path: str = Field(..., description="接口路径")
    project_id: int = Field(..., description="项目ID")
    category_id: int = Field(..., description="分类ID")
    status: str = Field(default="done", description="接口状态")
    
    # 请求参数
    req_params: List[APIParameter] = Field(default_factory=list, description="请求参数")
    req_query: List[APIParameter] = Field(default_factory=list, description="查询参数")
    req_headers: List[APIParameter] = Field(default_factory=list, description="请求头")
    req_body_type: Optional[str] = Field(None, description="请求体类型")
    req_body_form: List[APIParameter] = Field(default_factory=list, description="表单参数")
    req_body_other: Optional[str] = Field(None, description="其他请求体")
    
    # 响应参数
    res_body_type: Optional[str] = Field(None, description="响应体类型")
    res_body: Optional[str] = Field(None, description="响应体示例")
    
    # 时间戳
    add_time: Optional[datetime] = Field(None, description="创建时间")
    up_time: Optional[datetime] = Field(None, description="更新时间")
    
    # 用户信息
    username: Optional[str] = Field(None, description="创建用户")
    
    def get_full_description(self) -> str:
        """获取完整描述，用于向量化"""
        parts = []
        
        # 标题和描述
        if self.title:
            parts.append(f"标题: {self.title}")
        if self.description:
            parts.append(f"描述: {self.description}")
            
        # 接口信息
        parts.append(f"方法: {self.method}")
        parts.append(f"路径: {self.path}")
        
        # 参数信息
        if self.req_params:
            param_desc = ", ".join([f"{p.name}({p.type})" for p in self.req_params])
            parts.append(f"路径参数: {param_desc}")
            
        if self.req_query:
            query_desc = ", ".join([f"{p.name}({p.type})" for p in self.req_query])
            parts.append(f"查询参数: {query_desc}")
            
        if self.req_body_form:
            form_desc = ", ".join([f"{p.name}({p.type})" for p in self.req_body_form])
            parts.append(f"表单参数: {form_desc}")
            
        return " | ".join(parts)


class APIInterfaceList(BaseModel):
    """API接口列表响应模型"""
    errcode: int = Field(..., description="错误码")
    errmsg: str = Field(..., description="错误信息")
    data: Dict[str, Any] = Field(..., description="数据")
    
    def get_interfaces(self) -> List[APIInterface]:
        """从响应数据中提取接口列表"""
        interfaces = []
        if self.data and "list" in self.data:
            for item in self.data["list"]:
                try:
                    interface = APIInterface(**item)
                    interfaces.append(interface)
                except Exception as e:
                    print(f"解析接口数据失败: {e}, 数据: {item}")
        return interfaces


class VectorRecord(BaseModel):
    """向量记录模型"""
    id: str = Field(..., description="记录ID")
    interface_id: int = Field(..., description="接口ID")
    title: str = Field(..., description="接口标题")
    description: str = Field(..., description="完整描述")
    vector: List[float] = Field(..., description="向量数据")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
