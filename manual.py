#!/usr/bin/env python3
"""
VDB Server 主程序
支持交互式执行各种同步操作
"""

import uuid
import time
import httpx
import json
import sys
from typing import Dict, Any, Optional

# 导入配置和日志
from config import config
from logger_config import sync_logger as logger

# 删除所有接口信息，随后重建（进行全量同步）
def full_rebase():
    """删除所有接口信息，随后重建（进行全量同步）"""
    logger.info('开始执行全量重建操作')
    logger.info('开始删除所有接口信息')

    try:
        delete_url = config.VDB_BASE_URL + 'schema/' + config.VDB_CLASS_NAME
        delete_response = httpx.delete(delete_url, headers=config.VDB_HEADERS, timeout=config.REQUEST_TIMEOUT)
        logger.info(f'删除结果: {delete_response.status_code} - {delete_response.text}')

        if delete_response.status_code == 200:
            logger.info('接口信息删除成功，开始全量同步')
            full_sync()
        else:
            logger.error(f'删除接口信息失败，状态码: {delete_response.status_code}')
            return False

    except Exception as e:
        logger.error(f'全量重建过程中发生错误: {str(e)}')
        return False

    logger.info('全量重建操作完成')
    return True

# 将分类下所有接口全量同步（不会同步删除接口的动作）
def full_sync():
    """将分类下所有接口全量同步（不会同步删除接口的动作）"""
    logger.info('开始执行全量同步操作')

    try:
        # 查询分类下所有接口
        list_url = config.YAPI_BASE_URL + 'list_cat?catid=' + config.YAPI_CATID + '&token=' + config.YAPI_TOKEN
        logger.info(f'请求接口列表: {list_url}')

        list_response = httpx.get(list_url, timeout=config.REQUEST_TIMEOUT)
        if list_response.status_code != 200:
            logger.error(f'获取接口列表失败，状态码: {list_response.status_code}')
            return False

        list_data = list_response.json()
        interface_info_list = list_data.get('data', {}).get('list', [])

        if not interface_info_list:
            logger.warning('未找到任何接口信息')
            return True

        logger.info(f'接口总数: {len(interface_info_list)}')

        success_count = 0
        failed_count = 0

        for i, interface_info in enumerate(interface_info_list, 1):
            interface_id = interface_info.get('_id')
            logger.info(f'处理接口 {i}/{len(interface_info_list)}: {interface_id}')

            if single_sync(interface_id):
                success_count += 1
            else:
                failed_count += 1

            logger.info('-' * 60)

        logger.info(f'全量同步执行完毕 - 成功: {success_count}, 失败: {failed_count}')
        return failed_count == 0

    except Exception as e:
        logger.error(f'全量同步过程中发生错误: {str(e)}')
        return False

# 同步单个接口信息（不会同步删除接口的动作）
def single_sync(interface_id):
    """同步单个接口信息（不会同步删除接口的动作）"""
    logger.info(f'开始同步接口: {interface_id}')

    try:
        # 获取接口详情
        interface_detail_response = get_interface_detail_by_id(str(interface_id))
        if not interface_detail_response or 'data' not in interface_detail_response:
            logger.error(f'获取接口 {interface_id} 详情失败')
            return False

        interface_detail = interface_detail_response.get('data')
        if not interface_detail:
            logger.error(f'接口 {interface_id} 详情为空')
            return False

        # 提取接口信息
        interface_title = interface_detail.get('title', '')
        interface_markdown = interface_detail.get('markdown', '')
        interface_description = str(interface_title) + '\n' + str(interface_markdown)

        query_path = interface_detail.get('query_path', {})
        interface_uri = query_path.get('path', '') if query_path else ''
        interface_method = interface_detail.get('method', '')

        # 处理请求体信息
        req_body_other = interface_detail.get('req_body_other', '{}')
        interface_req_body = {}
        interface_req_body_required = []

        if req_body_other:
            try:
                req_body_data = json.loads(req_body_other)
                interface_req_body = req_body_data.get('properties', {})
                interface_req_body_required = interface_req_body.get('required', [])
            except (json.JSONDecodeError, AttributeError) as e:
                logger.warning(f'解析接口 {interface_id} 请求体信息失败: {str(e)}')

        logger.info(f'接口信息 - 标题: {interface_title}, 方法: {interface_method}, URI: {interface_uri}')

        # 标题描述进行向量化
        vector = text2vec(interface_description)
        if not vector:
            logger.error(f'接口 {interface_id} 向量化失败')
            return False

        content = f'###{interface_description}\n调用方式：{interface_method}\nuri:{interface_uri}\n参数说明{interface_req_body}\n不可空的参数列表：{interface_req_body_required}'

        # 准备数据
        _data = {
            "class": config.VDB_CLASS_NAME,
            "properties": {
                "apiDescription": interface_description,
                "apiDetails": content
            },
            "id": text2uuid(interface_id),
            "creationTimeUnix": int(time.time() * 1000),
            "lastUpdateTimeUnix": int(time.time() * 1000),
            "vector": vector
        }

        # 先尝试insert
        insert_url = config.VDB_BASE_URL + 'objects'
        insert_response = httpx.post(insert_url, json=_data, headers=config.VDB_HEADERS, timeout=config.REQUEST_TIMEOUT)
        insert_status = insert_response.status_code
        logger.info(f'insert结果: {insert_status}')

        if insert_status == 200:
            logger.info(f'接口 {interface_id} 同步成功（新增）')
            return True
        else:
            # insert失败，尝试update
            logger.info('已存在对象，尝试update')
            update_url = config.VDB_BASE_URL + 'objects/' + config.VDB_CLASS_NAME + '/' + text2uuid(interface_id)
            update_response = httpx.patch(update_url, json=_data, headers=config.VDB_HEADERS, timeout=config.REQUEST_TIMEOUT)
            update_status = update_response.status_code
            logger.info(f'update结果: {update_status}')

            if update_status == 204:
                logger.info(f'接口 {interface_id} 同步成功（更新）')
                return True
            else:
                logger.error(f'接口 {interface_id} 同步失败 - insert: {insert_status}, update: {update_status}')
                return False

    except Exception as e:
        logger.error(f'同步接口 {interface_id} 时发生错误: {str(e)}')
        return False

# 删除单个接口信息
def single_delete(interface_id):
    """删除单个接口信息"""
    logger.info(f'开始删除接口: {interface_id}')

    try:
        uuid_str = text2uuid(interface_id)
        delete_url = config.VDB_BASE_URL + 'objects/' + config.VDB_CLASS_NAME + '/' + uuid_str
        logger.info(f'删除URL: {delete_url}')

        delete_response = httpx.delete(delete_url, headers=config.VDB_HEADERS, timeout=config.REQUEST_TIMEOUT)
        delete_status = delete_response.status_code
        logger.info(f'delete结果: {delete_status}')

        if delete_status == 200:
            logger.info(f'接口 {interface_id} 删除成功')
            return True
        else:
            logger.error(f'接口 {interface_id} 删除失败，状态码: {delete_status}')
            return False

    except Exception as e:
        logger.error(f'删除接口 {interface_id} 时发生错误: {str(e)}')
        return False

# 根据接口id查询接口详细信息
def get_interface_detail_by_id(interface_id):
    """根据接口id查询接口详细信息"""
    try:
        url = config.YAPI_BASE_URL + 'get?id=' + interface_id + '&token=' + config.YAPI_TOKEN
        logger.debug(f'请求接口详情: {url}')

        response = httpx.get(url, timeout=config.REQUEST_TIMEOUT)
        if response.status_code != 200:
            logger.error(f'获取接口详情失败，状态码: {response.status_code}')
            return None

        return response.json()

    except Exception as e:
        logger.error(f'获取接口 {interface_id} 详情时发生错误: {str(e)}')
        return None

# 文本转向量
def text2vec(data):
    """文本转向量"""
    try:
        embed_data = {"input": data}
        logger.debug(f'开始向量化文本，长度: {len(str(data))}')

        embed_response = httpx.post(config.EMBED_URL, json=embed_data, headers=config.EMBED_HEADERS, timeout=config.REQUEST_TIMEOUT)
        if embed_response.status_code != 200:
            logger.error(f'向量化失败，状态码: {embed_response.status_code}')
            return None

        embed_result = embed_response.json()
        vector = embed_result['data'][0]['embedding']
        logger.debug(f'向量化成功，向量维度: {len(vector)}')

        return vector

    except Exception as e:
        logger.error(f'文本向量化时发生错误: {str(e)}')
        return None

# 根据文本生成uuid，相同的文本生成的uuid相同
def text2uuid(text):
    """根据文本生成uuid，相同的文本生成的uuid相同"""
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, str(text)))


def show_menu():
    """显示操作菜单"""
    print("\n" + "="*50)
    print("VDB Server 接口同步工具")
    print("="*50)
    print("请选择要执行的操作：")
    print("1. 全量重建 (full_rebase) - 删除所有数据后重新同步")
    print("2. 全量同步 (full_sync) - 同步分类下所有接口")
    print("3. 单个同步 (single_sync) - 同步指定接口")
    print("4. 单个删除 (single_delete) - 删除指定接口")
    print("5. 查看配置信息")
    print("0. 退出程序")
    print("="*50)

def get_user_input(prompt, input_type=str, required=True):
    """获取用户输入"""
    while True:
        try:
            user_input = input(prompt).strip()

            if not user_input and required:
                print("❌ 输入不能为空，请重新输入")
                continue
            elif not user_input and not required:
                return None

            if input_type == int:
                return int(user_input)
            else:
                return user_input

        except ValueError:
            print("❌ 输入格式错误，请输入有效的数字")
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            sys.exit(0)

def confirm_operation(operation_name):
    """确认操作"""
    while True:
        confirm = input(f"确认执行 {operation_name} 操作吗？(y/n): ").strip().lower()
        if confirm in ['y', 'yes', '是']:
            return True
        elif confirm in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y/yes/是 或 n/no/否")

def show_config():
    """显示配置信息"""
    print("\n" + "="*50)
    print("当前配置信息")
    print("="*50)

    config_info = config.get_all_config()
    for key, value in config_info.items():
        print(f"{key}: {value}")

    print("="*50)

def main():
    """主程序"""
    logger.info("VDB Server 启动")

    # 验证配置
    if not config.validate_config():
        logger.error("配置验证失败，程序退出")
        sys.exit(1)

    logger.info("配置验证通过")

    while True:
        try:
            show_menu()
            choice = get_user_input("请输入选项 (0-5): ", int)

            if choice == 0:
                print("感谢使用，再见！")
                logger.info("程序正常退出")
                break

            elif choice == 1:
                # 全量重建
                print("\n⚠️  警告：全量重建将删除所有现有数据！")
                if confirm_operation("全量重建"):
                    logger.info("用户确认执行全量重建")
                    start_time = time.time()
                    success = full_rebase()
                    end_time = time.time()

                    if success:
                        print(f"✅ 全量重建完成，耗时: {end_time - start_time:.2f} 秒")
                    else:
                        print("❌ 全量重建失败，请查看日志")
                else:
                    print("操作已取消")

            elif choice == 2:
                # 全量同步
                if confirm_operation("全量同步"):
                    logger.info("用户确认执行全量同步")
                    start_time = time.time()
                    success = full_sync()
                    end_time = time.time()

                    if success:
                        print(f"✅ 全量同步完成，耗时: {end_time - start_time:.2f} 秒")
                    else:
                        print("❌ 全量同步失败，请查看日志")
                else:
                    print("操作已取消")

            elif choice == 3:
                # 单个同步
                interface_id = get_user_input("请输入接口ID: ", int)

                if confirm_operation(f"同步接口 {interface_id}"):
                    logger.info(f"用户确认同步接口 {interface_id}")
                    start_time = time.time()
                    success = single_sync(interface_id)
                    end_time = time.time()

                    if success:
                        print(f"✅ 接口 {interface_id} 同步完成，耗时: {end_time - start_time:.2f} 秒")
                    else:
                        print(f"❌ 接口 {interface_id} 同步失败，请查看日志")
                else:
                    print("操作已取消")

            elif choice == 4:
                # 单个删除
                interface_id = get_user_input("请输入要删除的接口ID: ", int)

                if confirm_operation(f"删除接口 {interface_id}"):
                    logger.info(f"用户确认删除接口 {interface_id}")
                    start_time = time.time()
                    success = single_delete(interface_id)
                    end_time = time.time()

                    if success:
                        print(f"✅ 接口 {interface_id} 删除完成，耗时: {end_time - start_time:.2f} 秒")
                    else:
                        print(f"❌ 接口 {interface_id} 删除失败，请查看日志")
                else:
                    print("操作已取消")

            elif choice == 5:
                # 查看配置
                show_config()

            else:
                print("❌ 无效选项，请重新选择")

        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            logger.info("程序被用户中断")
            break
        except Exception as e:
            print(f"❌ 程序执行出错: {str(e)}")
            logger.error(f"程序执行出错: {str(e)}")

if __name__ == '__main__':
    main()
