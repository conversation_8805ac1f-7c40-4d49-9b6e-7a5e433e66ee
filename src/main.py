"""VDB-Server 主应用"""
import asyncio
import sys
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
import uvicorn

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import get_settings
from src.services.knowledge_base_service import KnowledgeBaseService
from src.api import routes


# 全局服务实例
kb_service: KnowledgeBaseService = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global kb_service
    
    settings = get_settings()
    
    # 启动时初始化
    logger.info("正在启动 VDB-Server...")
    
    try:
        # 创建数据目录
        Path(settings.vector_db_path).parent.mkdir(parents=True, exist_ok=True)
        if settings.log_file:
            Path(settings.log_file).parent.mkdir(parents=True, exist_ok=True)
            
        # 初始化知识库服务
        kb_service = KnowledgeBaseService(
            yapi_base_url=settings.yapi_base_url,
            yapi_token=settings.yapi_token,
            embedding_model=settings.embedding_model,
            vector_db_path=settings.vector_db_path
        )
        
        await kb_service.initialize()
        
        # 将服务实例设置到路由模块
        routes.kb_service = kb_service
        
        logger.info("VDB-Server 启动完成")
        
    except Exception as e:
        logger.error(f"VDB-Server 启动失败: {e}")
        raise
        
    yield
    
    # 关闭时清理
    logger.info("正在关闭 VDB-Server...")
    
    try:
        if kb_service:
            await kb_service.close()
        logger.info("VDB-Server 已关闭")
    except Exception as e:
        logger.error(f"关闭 VDB-Server 时出错: {e}")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    settings = get_settings()
    
    # 配置日志
    configure_logging(settings)
    
    # 创建应用
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="接口信息知识库构建服务 - 基于向量数据库的API接口知识管理系统",
        lifespan=lifespan
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册路由
    app.include_router(
        routes.router,
        prefix=settings.api_prefix,
        tags=["VDB-Server"]
    )
    
    # 根路径
    @app.get("/")
    async def root():
        return {
            "service": settings.app_name,
            "version": settings.app_version,
            "status": "running",
            "docs": "/docs",
            "api": settings.api_prefix
        }
    
    return app


def configure_logging(settings):
    """配置日志"""
    # 移除默认处理器
    logger.remove()
    
    # 控制台输出
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # 文件输出
    if settings.log_file:
        logger.add(
            settings.log_file,
            level=settings.log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation=settings.log_rotation,
            retention=settings.log_retention,
            compression="zip"
        )


# 创建应用实例
app = create_app()


def main():
    """主函数"""
    settings = get_settings()
    
    logger.info(f"启动 {settings.app_name} v{settings.app_version}")
    logger.info(f"服务器地址: http://{settings.host}:{settings.port}")
    logger.info(f"API文档: http://{settings.host}:{settings.port}/docs")
    logger.info(f"YAPI服务器: {settings.yapi_base_url}")
    logger.info(f"向量数据库路径: {settings.vector_db_path}")
    logger.info(f"嵌入模型: {settings.embedding_model}")
    
    # 启动服务器
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        workers=settings.workers,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )


if __name__ == "__main__":
    main()
