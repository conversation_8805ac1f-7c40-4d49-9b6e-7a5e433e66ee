import uuid
import time
import httpx
import json

# yapi基础信息配置
yapi_base_url = 'http://10.0.107.253:3000/api/interface/'
yapi_token = 'e84e14ddd978c534dd2aa872b4e5ae79e989dcea0d24b47b6da1686b96b81d84'
yapi_catid = '303'

# 向量数据库配置
vdb_base_url = 'http://10.20.35.250:8009/v1/'
vdb_headers = {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'Authorization': 'Bearer QTsBTQWbr6iESeOoVKgiejFDhQogiSON'
}
vdb_class_name = 'Zhuang_test'

# 嵌入模型配置
embed_url = 'https://ai.secsign.online:3003/v1/embeddings'
embed_headers = {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'Authorization': 'Bearer sk-hBB0Icl2AlA58nOuHydvHRX33Cagdl3etG04k0AI4F4fzDvG'
}

# 删除所有接口信息，随后重建（进行全量同步）
def full_rebase():
    print('开始删除所有接口信息')
    delete_url = vdb_base_url + 'schema/' + vdb_class_name
    delete_status = httpx.delete(delete_url, headers=vdb_headers)
    print('删除结果:' + str(delete_status))

    full_sync()

# 将分类下所有接口全量同步（不会同步删除接口的动作）
def full_sync():
    # 查询分类下所有接口
    print('开始全量同步')
    list_url = yapi_base_url + 'list_cat?catid=' + yapi_catid + '&token=' + yapi_token
    list_reponse = httpx.get(list_url).json()
    interface_info_list = list_reponse.get('data').get('list')
    print('接口总数：' + str(len(interface_info_list)))
    for interface_info in interface_info_list:
        interface_id = interface_info.get('_id')
        single_sync(interface_id)
        print('-------------------------------------------------------------------')
    print('全量同步执行完毕')

# 同步单个接口信息（不会同步删除接口的动作）
def single_sync(interface_id):
    print('开始同步接口: ' + str(interface_id))
    interface_detail = get_interface_detail_by_id(str(interface_id)).get('data')
    interface_description = str(interface_detail.get('title')) + '\n' + str(interface_detail.get('markdown'))
    interface_uri = interface_detail.get('query_path').get('path')

    interface_method = interface_detail.get('method')
    interface_req_body = json.loads(interface_detail.get('req_body_other')).get('properties')
    interface_req_body_required = interface_req_body.get('required')

    # 标题描述进行向量化
    vector = text2vec(interface_description)
    content = '###' + str(interface_description) + '\n调用方式：' + str(interface_method) + '\nuri:' + str(
        interface_uri) + '\n参数说明' + str(interface_req_body) + '\n不可空的参数列表：' + str(
        interface_req_body_required)

    # insert或update向量数据库
    # 先尝试insert
    insert_url = vdb_base_url + 'objects'
    _data = {
        "class": vdb_class_name,
        "properties": {
            "apiDescription": interface_description,
            "apiDetails": content
        },
        # uuid根据interface_id生成，相同的interface_id生成的uuid相同
        "id": text2uuid(interface_id),
        "creationTimeUnix": int(time.time() * 1000),
        "lastUpdateTimeUnix": int(time.time() * 1000),
        "vector": vector
    }
    insert_status = httpx.post(insert_url, json=_data, headers=vdb_headers).status_code
    print('insert结果:' + str(insert_status))

    # insert失败，尝试update
    if insert_status != 200:
        print('已存在对象，尝试update')
        update_url = vdb_base_url + 'objects/' + vdb_class_name + '/' + text2uuid(interface_id)
        update_status = httpx.patch(update_url, json=_data, headers=vdb_headers).status_code
        print('update结果:' + str(update_status))

# 删除单个接口信息
def single_delete(interface_id):
    uuid = text2uuid(interface_id)
    delete_url = vdb_base_url + 'objects/' + vdb_class_name + '/' + uuid
    delete_status = httpx.delete(delete_url, headers=vdb_headers).status_code
    print('delete结果:' + str(delete_status))

# 根据接口id查询接口详细信息
def get_interface_detail_by_id(interface_id):
    url = yapi_base_url + 'get?id=' + interface_id + '&token=' + yapi_token
    reponse = httpx.get(url).json()
    return reponse

# 文本转向量
def text2vec(data):
    embed_data = {
        "input": data
    }
    embed_reponse = httpx.post(embed_url, json=embed_data, headers=embed_headers).json()

    vector = embed_reponse['data'][0]['embedding']

    return vector

# 根据文本生成uuid，相同的文本生成的uuid相同
def text2uuid(text):
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, str(text)))


if __name__ == '__main__':
    full_sync()
    # single_sync(1103)
    # single_delete(1103)
    # full_rebase()
