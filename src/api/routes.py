"""API路由定义"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Query, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from loguru import logger

from ..services.knowledge_base_service import KnowledgeBaseService
from config.settings import get_settings

# 创建路由器
router = APIRouter()

# 全局服务实例（在应用启动时初始化）
kb_service: Optional[KnowledgeBaseService] = None


def get_kb_service() -> KnowledgeBaseService:
    """获取知识库服务实例"""
    if kb_service is None:
        raise HTTPException(status_code=500, detail="知识库服务未初始化")
    return kb_service


# 请求/响应模型
class SyncProjectRequest(BaseModel):
    """同步项目请求"""
    project_id: int


class SyncInterfaceRequest(BaseModel):
    """同步接口请求"""
    interface_id: int


class SearchRequest(BaseModel):
    """搜索请求"""
    query: str
    top_k: int = 10
    project_id: Optional[int] = None


class APIResponse(BaseModel):
    """API响应基类"""
    success: bool
    message: str
    data: Optional[Any] = None


# 健康检查
@router.get("/health", response_model=Dict[str, str])
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "VDB-Server"}


# 获取统计信息
@router.get("/stats", response_model=APIResponse)
async def get_statistics(service: KnowledgeBaseService = Depends(get_kb_service)):
    """获取知识库统计信息"""
    try:
        stats = await service.get_statistics()
        return APIResponse(
            success=True,
            message="获取统计信息成功",
            data=stats
        )
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 同步项目接口
@router.post("/sync/project", response_model=APIResponse)
async def sync_project_interfaces(
    request: SyncProjectRequest,
    background_tasks: BackgroundTasks,
    service: KnowledgeBaseService = Depends(get_kb_service)
):
    """同步项目接口到知识库"""
    try:
        # 在后台任务中执行同步
        background_tasks.add_task(
            _sync_project_background,
            service,
            request.project_id
        )
        
        return APIResponse(
            success=True,
            message=f"项目 {request.project_id} 同步任务已启动",
            data={"project_id": request.project_id}
        )
    except Exception as e:
        logger.error(f"启动项目同步任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _sync_project_background(service: KnowledgeBaseService, project_id: int):
    """后台同步项目接口"""
    try:
        result = await service.sync_project_interfaces(project_id)
        logger.info(f"项目 {project_id} 同步完成: {result}")
    except Exception as e:
        logger.error(f"后台同步项目 {project_id} 失败: {e}")


# 同步单个接口
@router.post("/sync/interface", response_model=APIResponse)
async def sync_interface(
    request: SyncInterfaceRequest,
    service: KnowledgeBaseService = Depends(get_kb_service)
):
    """同步单个接口到知识库"""
    try:
        result = await service.sync_interface(request.interface_id)
        
        if result["success"]:
            return APIResponse(
                success=True,
                message=f"接口 {request.interface_id} 同步成功",
                data=result
            )
        else:
            return APIResponse(
                success=False,
                message=f"接口 {request.interface_id} 同步失败: {result.get('error', '未知错误')}",
                data=result
            )
    except Exception as e:
        logger.error(f"同步接口失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 更新接口
@router.put("/interface/{interface_id}", response_model=APIResponse)
async def update_interface(
    interface_id: int,
    service: KnowledgeBaseService = Depends(get_kb_service)
):
    """更新接口信息"""
    try:
        result = await service.update_interface(interface_id)
        
        if result["success"]:
            return APIResponse(
                success=True,
                message=f"接口 {interface_id} 更新成功",
                data=result
            )
        else:
            return APIResponse(
                success=False,
                message=f"接口 {interface_id} 更新失败: {result.get('error', '未知错误')}",
                data=result
            )
    except Exception as e:
        logger.error(f"更新接口失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 删除接口
@router.delete("/interface/{interface_id}", response_model=APIResponse)
async def delete_interface(
    interface_id: int,
    service: KnowledgeBaseService = Depends(get_kb_service)
):
    """删除接口"""
    try:
        result = await service.delete_interface(interface_id)
        
        if result["success"]:
            return APIResponse(
                success=True,
                message=f"接口 {interface_id} 删除成功",
                data=result
            )
        else:
            return APIResponse(
                success=False,
                message=f"接口 {interface_id} 删除失败: {result.get('error', '未知错误')}",
                data=result
            )
    except Exception as e:
        logger.error(f"删除接口失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 搜索接口
@router.post("/search", response_model=APIResponse)
async def search_interfaces(
    request: SearchRequest,
    service: KnowledgeBaseService = Depends(get_kb_service)
):
    """搜索相似接口"""
    try:
        settings = get_settings()
        
        # 限制搜索数量
        top_k = min(request.top_k, settings.max_search_limit)
        
        results = await service.search_interfaces(
            query=request.query,
            top_k=top_k,
            project_id=request.project_id
        )
        
        return APIResponse(
            success=True,
            message=f"搜索完成，找到 {len(results)} 个相似接口",
            data={
                "query": request.query,
                "results": results,
                "total": len(results)
            }
        )
    except Exception as e:
        logger.error(f"搜索接口失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# GET方式搜索接口（便于测试）
@router.get("/search", response_model=APIResponse)
async def search_interfaces_get(
    q: str = Query(..., description="搜索查询"),
    limit: int = Query(10, description="结果数量限制"),
    project_id: Optional[int] = Query(None, description="项目ID过滤"),
    service: KnowledgeBaseService = Depends(get_kb_service)
):
    """GET方式搜索相似接口"""
    try:
        settings = get_settings()
        
        # 限制搜索数量
        top_k = min(limit, settings.max_search_limit)
        
        results = await service.search_interfaces(
            query=q,
            top_k=top_k,
            project_id=project_id
        )
        
        return APIResponse(
            success=True,
            message=f"搜索完成，找到 {len(results)} 个相似接口",
            data={
                "query": q,
                "results": results,
                "total": len(results)
            }
        )
    except Exception as e:
        logger.error(f"搜索接口失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 重建知识库
@router.post("/rebuild", response_model=APIResponse)
async def rebuild_knowledge_base(
    background_tasks: BackgroundTasks,
    service: KnowledgeBaseService = Depends(get_kb_service)
):
    """重建知识库"""
    try:
        # 在后台任务中执行重建
        background_tasks.add_task(_rebuild_knowledge_base_background, service)
        
        return APIResponse(
            success=True,
            message="知识库重建任务已启动",
            data={}
        )
    except Exception as e:
        logger.error(f"启动知识库重建任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _rebuild_knowledge_base_background(service: KnowledgeBaseService):
    """后台重建知识库"""
    try:
        result = await service.rebuild_knowledge_base()
        logger.info(f"知识库重建完成: {result}")
    except Exception as e:
        logger.error(f"后台重建知识库失败: {e}")


# YAPI Webhook接口（用于接收YAPI的通知）
@router.post("/webhook/yapi", response_model=APIResponse)
async def yapi_webhook(
    payload: Dict[str, Any],
    background_tasks: BackgroundTasks,
    service: KnowledgeBaseService = Depends(get_kb_service)
):
    """处理YAPI Webhook通知"""
    try:
        logger.info(f"收到YAPI Webhook通知: {payload}")
        
        # 根据通知类型处理
        action = payload.get("action")
        interface_id = payload.get("interface_id")
        
        if not interface_id:
            return APIResponse(
                success=False,
                message="缺少interface_id参数",
                data=payload
            )
            
        if action in ["add", "update"]:
            # 同步或更新接口
            background_tasks.add_task(
                _handle_interface_change,
                service,
                interface_id,
                action
            )
        elif action == "delete":
            # 删除接口
            background_tasks.add_task(
                service.delete_interface,
                interface_id
            )
            
        return APIResponse(
            success=True,
            message=f"Webhook处理任务已启动: {action} interface {interface_id}",
            data=payload
        )
    except Exception as e:
        logger.error(f"处理YAPI Webhook失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _handle_interface_change(
    service: KnowledgeBaseService, 
    interface_id: int, 
    action: str
):
    """处理接口变更"""
    try:
        if action == "add":
            result = await service.sync_interface(interface_id)
        else:  # update
            result = await service.update_interface(interface_id)
            
        logger.info(f"接口 {interface_id} {action} 处理完成: {result}")
    except Exception as e:
        logger.error(f"处理接口 {interface_id} {action} 失败: {e}")
