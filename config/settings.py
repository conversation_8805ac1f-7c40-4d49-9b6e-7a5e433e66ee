"""应用配置管理"""
import os
from typing import Optional, List
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基本配置
    app_name: str = Field(default="VDB-Server", description="应用名称")
    app_version: str = Field(default="0.1.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器地址")
    port: int = Field(default=8000, description="服务器端口")
    workers: int = Field(default=1, description="工作进程数")
    
    # YAPI配置
    yapi_base_url: str = Field(..., description="YAPI服务器地址")
    yapi_token: Optional[str] = Field(None, description="YAPI访问令牌")
    
    # 嵌入模型配置
    embedding_model: str = Field(
        default="all-MiniLM-L6-v2", 
        description="嵌入模型名称"
    )
    embedding_device: str = Field(default="cpu", description="模型运行设备")
    
    # 向量数据库配置
    vector_db_path: str = Field(
        default="./data/chroma_db", 
        description="向量数据库存储路径"
    )
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: Optional[str] = Field(None, description="日志文件路径")
    log_rotation: str = Field(default="1 day", description="日志轮转")
    log_retention: str = Field(default="30 days", description="日志保留时间")
    
    # API配置
    api_prefix: str = Field(default="/api/v1", description="API路径前缀")
    cors_origins: List[str] = Field(
        default=["*"], 
        description="CORS允许的源"
    )
    
    # 安全配置
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="密钥"
    )
    access_token_expire_minutes: int = Field(
        default=30, 
        description="访问令牌过期时间（分钟）"
    )
    
    # 任务配置
    max_concurrent_tasks: int = Field(
        default=5, 
        description="最大并发任务数"
    )
    task_timeout: int = Field(default=300, description="任务超时时间（秒）")
    
    # 搜索配置
    default_search_limit: int = Field(default=10, description="默认搜索结果数量")
    max_search_limit: int = Field(default=100, description="最大搜索结果数量")
    similarity_threshold: float = Field(
        default=0.5, 
        description="相似度阈值"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        # 环境变量前缀
        env_prefix = "VDB_"


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings


# 配置验证
def validate_settings():
    """验证配置"""
    errors = []
    
    # 验证必需的配置
    if not settings.yapi_base_url:
        errors.append("YAPI_BASE_URL 是必需的配置")
        
    if not settings.secret_key or settings.secret_key == "your-secret-key-change-in-production":
        errors.append("SECRET_KEY 必须设置为安全的值")
        
    # 验证端口范围
    if not (1 <= settings.port <= 65535):
        errors.append("PORT 必须在 1-65535 范围内")
        
    # 验证日志级别
    valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    if settings.log_level.upper() not in valid_log_levels:
        errors.append(f"LOG_LEVEL 必须是以下值之一: {', '.join(valid_log_levels)}")
        
    # 验证相似度阈值
    if not (0.0 <= settings.similarity_threshold <= 1.0):
        errors.append("SIMILARITY_THRESHOLD 必须在 0.0-1.0 范围内")
        
    if errors:
        raise ValueError(f"配置验证失败:\n" + "\n".join(f"- {error}" for error in errors))


# 在导入时验证配置
try:
    validate_settings()
except ValueError as e:
    print(f"配置错误: {e}")
    # 在生产环境中可能需要退出程序
    # import sys
    # sys.exit(1)
