"""向量化服务"""
import asyncio
from typing import List, Optional, Union
from sentence_transformers import SentenceTransformer
from loguru import logger
import numpy as np

from ..models.api_interface import APIInterface


class EmbeddingService:
    """向量化服务"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        """
        初始化向量化服务
        
        Args:
            model_name: 嵌入模型名称
        """
        self.model_name = model_name
        self.model: Optional[SentenceTransformer] = None
        self._lock = asyncio.Lock()
        
    async def _load_model(self):
        """加载模型（懒加载）"""
        if self.model is None:
            async with self._lock:
                if self.model is None:
                    logger.info(f"加载嵌入模型: {self.model_name}")
                    # 在线程池中加载模型，避免阻塞事件循环
                    loop = asyncio.get_event_loop()
                    self.model = await loop.run_in_executor(
                        None, 
                        lambda: SentenceTransformer(self.model_name)
                    )
                    logger.info("嵌入模型加载完成")
                    
    async def encode_text(self, text: str) -> List[float]:
        """
        将文本编码为向量
        
        Args:
            text: 输入文本
            
        Returns:
            向量表示
        """
        await self._load_model()
        
        try:
            # 在线程池中执行编码，避免阻塞事件循环
            loop = asyncio.get_event_loop()
            embedding = await loop.run_in_executor(
                None,
                lambda: self.model.encode([text], convert_to_numpy=True)
            )
            
            # 返回第一个（也是唯一一个）向量
            vector = embedding[0].tolist()
            logger.debug(f"文本向量化完成，维度: {len(vector)}")
            
            return vector
            
        except Exception as e:
            logger.error(f"文本向量化失败: {e}")
            raise
            
    async def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """
        批量将文本编码为向量
        
        Args:
            texts: 输入文本列表
            
        Returns:
            向量列表
        """
        await self._load_model()
        
        try:
            # 在线程池中执行批量编码
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                None,
                lambda: self.model.encode(texts, convert_to_numpy=True)
            )
            
            # 转换为列表格式
            vectors = [embedding.tolist() for embedding in embeddings]
            logger.info(f"批量向量化完成，处理 {len(texts)} 个文本")
            
            return vectors
            
        except Exception as e:
            logger.error(f"批量文本向量化失败: {e}")
            raise
            
    async def encode_interface(self, interface: APIInterface) -> List[float]:
        """
        将API接口信息编码为向量
        
        Args:
            interface: API接口对象
            
        Returns:
            向量表示
        """
        try:
            # 获取接口的完整描述
            description = interface.get_full_description()
            logger.debug(f"接口描述: {description}")
            
            # 向量化描述
            vector = await self.encode_text(description)
            
            logger.info(f"接口 {interface.id} 向量化完成")
            return vector
            
        except Exception as e:
            logger.error(f"接口向量化失败: {e}")
            raise
            
    async def encode_interfaces(self, interfaces: List[APIInterface]) -> List[List[float]]:
        """
        批量将API接口信息编码为向量
        
        Args:
            interfaces: API接口列表
            
        Returns:
            向量列表
        """
        try:
            # 获取所有接口的描述
            descriptions = [interface.get_full_description() for interface in interfaces]
            
            # 批量向量化
            vectors = await self.encode_texts(descriptions)
            
            logger.info(f"批量接口向量化完成，处理 {len(interfaces)} 个接口")
            return vectors
            
        except Exception as e:
            logger.error(f"批量接口向量化失败: {e}")
            raise
            
    def calculate_similarity(
        self, 
        vector1: List[float], 
        vector2: List[float]
    ) -> float:
        """
        计算两个向量的余弦相似度
        
        Args:
            vector1: 向量1
            vector2: 向量2
            
        Returns:
            相似度分数 (0-1)
        """
        try:
            # 转换为numpy数组
            v1 = np.array(vector1)
            v2 = np.array(vector2)
            
            # 计算余弦相似度
            dot_product = np.dot(v1, v2)
            norm_v1 = np.linalg.norm(v1)
            norm_v2 = np.linalg.norm(v2)
            
            if norm_v1 == 0 or norm_v2 == 0:
                return 0.0
                
            similarity = dot_product / (norm_v1 * norm_v2)
            
            # 确保结果在 [0, 1] 范围内
            similarity = max(0.0, min(1.0, (similarity + 1) / 2))
            
            return float(similarity)
            
        except Exception as e:
            logger.error(f"计算相似度失败: {e}")
            return 0.0
            
    async def find_similar_interfaces(
        self,
        query_vector: List[float],
        interface_vectors: List[tuple],  # (interface_id, vector)
        top_k: int = 10,
        threshold: float = 0.5
    ) -> List[tuple]:  # (interface_id, similarity_score)
        """
        查找相似的接口
        
        Args:
            query_vector: 查询向量
            interface_vectors: 接口向量列表 [(interface_id, vector), ...]
            top_k: 返回前K个结果
            threshold: 相似度阈值
            
        Returns:
            相似接口列表 [(interface_id, similarity_score), ...]
        """
        try:
            similarities = []
            
            for interface_id, vector in interface_vectors:
                similarity = self.calculate_similarity(query_vector, vector)
                if similarity >= threshold:
                    similarities.append((interface_id, similarity))
                    
            # 按相似度降序排序
            similarities.sort(key=lambda x: x[1], reverse=True)
            
            # 返回前K个结果
            result = similarities[:top_k]
            
            logger.info(f"找到 {len(result)} 个相似接口")
            return result
            
        except Exception as e:
            logger.error(f"查找相似接口失败: {e}")
            return []
